import envConfig from "@/lib/config.env";
import {
    GenericResponse,
    PaymentRequestInput,
    ZarinpalPaymentRequestResponse,
    ZarinpalVarificationResponse
} from "@/lib/types";
import FetchApi from "@/lib/fetch-api";
import {handleInquiryServiceError, getAppSource} from "@/utils/helpers-server";
import applicationRepository from "@/features/application/application.repository";
import {connectToDatabase} from "@/lib/mongodb";

class ZarinpalService {
    fetchApiInstance: FetchApi;
    isDevelopment: boolean;
    merchantId: string;

    constructor() {
        this.fetchApiInstance = new FetchApi()
        const env = envConfig();
        this.isDevelopment = false;
        this.merchantId = env.ZARINPAL.MERCHANT_ID_PRODUCTION

        // if (!this.isDevelopment) {
        //     this.merchantId = env.ZARINPAL.MERCHANT_ID_PRODUCTION
        // } else {
        //     this.fetchApiInstance.setHeader("X-SANDBOX", "1")
        //     this.merchantId = env.ZARINPAL.MERCHANT_ID_DEVELOPMENT;
        // }
    }

    async getMerchantId(): Promise<string> {
        try {
            await connectToDatabase();
            const source = await getAppSource();
            console.log('ZarinPal - Current source:', source);

            const application = await applicationRepository.getByHost(source);
            console.log('ZarinPal - Application from DB:', application);

            // اگر merchant_id در دیتابیس موجود است، از آن استفاده کن
            if (application?.merchent_id) {
                console.log('ZarinPal - Using merchant ID from DB:', application.merchent_id);
                return application.merchent_id;
            }

            // در غیر این صورت از merchant_id پیش‌فرض استفاده کن
            console.log('ZarinPal - Using default merchant ID:', this.merchantId);
            return this.merchantId;
        } catch (error) {
            console.error('Error getting merchant ID from database:', error);
            console.log('ZarinPal - Using fallback merchant ID:', this.merchantId);
            // در صورت خطا، از merchant_id پیش‌فرض استفاده کن
            return this.merchantId;
        }
    }

    async paymentRequest({
                             callbackUrl,
                             amount,
                             description
                         }: PaymentRequestInput): Promise<GenericResponse<ZarinpalPaymentRequestResponse | undefined>> {
        try {
            const merchantId = await this.getMerchantId();

            const url = `https://${this.isDevelopment ? "sandbox" : "payment"}.zarinpal.com/pg/v4/payment/request.json`
            console.log('ZarinPal Payment Request - URL:', url);

            const responseResult =
                await this.fetchApiInstance.post<{
                    data: ZarinpalPaymentRequestResponse,
                    error: any[]
                }>(url, {
                    merchant_id: merchantId,
                    amount,
                    callback_url: callbackUrl,
                    description: "پرداخت به کیف پول",
                    currency: 'IRT',
                })

            console.log('ZarinPal Payment Request - Response:', responseResult);
            const {message, code, authority, fee, fee_type} = responseResult.data

            if (code === 100) {
                const gatewayUrl = `https://${this.isDevelopment ? 'sandbox' : 'www'}.zarinpal.com/pg/StartPay/${authority}`
                return {
                    success: 'SUCCESS',
                    data: {
                        authority,
                        url: gatewayUrl,
                        message: message,
                        fee,
                        fee_type,
                        code
                    }
                }
            }

            return {
                success: 'FAILED',
                message,
            }
        } catch (error: any) {
            return handleInquiryServiceError(error, 'zarinpal.service.ts');
        }
    }

    async verifyPayment({amount, authority}: {
        amount: number,
        authority: string
    }): Promise<GenericResponse<ZarinpalVarificationResponse | undefined, {
        message: string,
        code: number
    } | undefined>> {

        try {
            const merchantId = await this.getMerchantId();
            const url = `https://${this.isDevelopment ? "sandbox" : "payment"}.zarinpal.com/pg/v4/payment/verify.json`

            const responseResult =
                await this.fetchApiInstance.post<{ data: ZarinpalVarificationResponse, error: any[] }>(url, {
                    merchant_id: merchantId,
                    amount: amount,
                    authority
                })

            console.log('zarinpal results >>>', responseResult)
            if (responseResult.data.code === 100) {
                return {
                    success: 'SUCCESS',
                    data: responseResult.data
                }
            }

            return {
                success: 'FAILED',
                message: responseResult.data.message,
                errors: {
                    code: responseResult.data.code,
                    message: responseResult.data.message
                }
            }

        } catch (error: any) {
            return handleInquiryServiceError(error, 'zarinpal.service.ts');
        }

    }

}

export default new ZarinpalService();
