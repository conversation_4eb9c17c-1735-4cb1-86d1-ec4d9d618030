import { InquiryTypeEnum } from "@/lib/types";

export function getServiceName(type: InquiryTypeEnum): string {
    switch (type) {
        // Jibit Services
        case InquiryTypeEnum.Jibit_CardToDeposit:
            return 'استعلام کارت به حساب';
        case InquiryTypeEnum.Jibit_DepositToIban:
            return 'استعلام حساب به شبا';
        case InquiryTypeEnum.Jibit_CardToIban:
            return 'استعلام کارت به شبا';
        case InquiryTypeEnum.Jibit_IbanInquiry:
            return 'استعلام شبا';
        case InquiryTypeEnum.Jibit_CardInquiry:
            return 'استعلام کارت';
        
        // Ghabzino Services
        case InquiryTypeEnum.Ghanzino_DepositToIban:
            return 'استعلام حساب به شبا';
        case InquiryTypeEnum.Ghanzino_CardToIban:
            return 'استعلام کارت به شبا';
        case InquiryTypeEnum.G<PERSON><PERSON><PERSON>_SayadCheckInquiry:
            return 'استعلام چک صیادی';
        case InquiryTypeEnum.Ghanzino_KhalafiKhodro:
            return 'استعلام خلافی خودرو';
        case InquiryTypeEnum.Ghanzino_KhalafiMotor:
            return 'استعلام خلافی موتور';
        
        default:
            return 'سرویس نامشخص';
    }
}
